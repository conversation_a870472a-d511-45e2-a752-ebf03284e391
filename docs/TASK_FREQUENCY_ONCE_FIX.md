# Task Frequency Consistency Fix

## Problem Description

There was an inconsistency between the Admin GraphQL API and the internal model for task frequency values:

- **Admin GraphQL Schema**: Originally used `ONCE` for one-time tasks
- **Internal Model**: Uses `ONE_TIME` for one-time tasks
- **User GraphQL Schema**: Uses `ONE_TIME` for one-time tasks

## Solution Applied

**Updated Admin GraphQL Schema to use `ONE_TIME` instead of `ONCE`** for consistency across all layers.

### Root Cause

When creating tasks via the Admin API with `frequency: ONCE`, the value was being cast directly to the model without proper mapping:

```go
// BEFORE (problematic)
task.Frequency = model.TaskFrequency(input.Frequency) // "ONCE" -> "ONCE" (mismatch)

// AFTER (fixed by schema change)
task.Frequency = model.TaskFrequency(input.Frequency) // "ONE_TIME" -> "ONE_TIME" (consistent)
```

### Impact

Tasks created with frequency `"ONCE"` were not properly recognized by the completion logic, which only checks for `model.FrequencyOneTime` (`"ONE_TIME"`). This caused:

1. One-time tasks could be completed multiple times
2. Completion validation logic failed to prevent duplicate completions
3. Inconsistent behavior between admin-created and system-created tasks

## Solution

### 1. Schema Changes

**Updated Admin GraphQL Schema**:

```graphql
# BEFORE
enum TaskFrequency {
  ONCE      # <- Inconsistent
  DAILY
  WEEKLY
  MONTHLY
  UNLIMITED
}

# AFTER
enum TaskFrequency {
  ONE_TIME  # <- Now consistent with model and user API
  DAILY
  WEEKLY
  MONTHLY
  UNLIMITED
}
```

### 2. Code Simplification

Since Admin GraphQL now uses `ONE_TIME` like the model, we can use direct casting:

```go
// Now works correctly without mapping
task.Frequency = model.TaskFrequency(input.Frequency) // "ONE_TIME" -> "ONE_TIME"
```

### 2. Data Migration

For existing tasks with incorrect frequency values, use the provided migration tools:

#### Option A: Using Go Script (Recommended)

```bash
# Check what would be fixed (dry run)
make fix-task-frequency-dry-run

# Apply the fix
make fix-task-frequency
```

#### Option B: Using SQL Script

```bash
# Run the SQL script directly
psql -d your_database -f scripts/fix_task_frequency_once_to_one_time.sql
```

### 3. Testing

Added comprehensive tests to verify the mapping works correctly:

```bash
go test ./internal/controller/admin/graphql/resolvers/ -v -run TestFrequencyMapping
```

## Files Changed

1. `internal/controller/admin/graphql/schemas/admin_activity_cashback.gql` - Updated enum from ONCE to ONE_TIME
2. `internal/controller/admin/graphql/resolvers/admin_activity_cashback.go` - Simplified to use direct casting
3. `internal/controller/graphql/resolvers/admin_activity_cashback.go` - Simplified to use direct casting
4. `cmd/fix-task-frequency/main.go` - Migration script (still needed for existing data)
5. `scripts/fix_task_frequency_once_to_one_time.sql` - SQL migration (still needed for existing data)
6. `internal/controller/admin/graphql/resolvers/frequency_mapping_test.go` - Updated tests
7. `Makefile` - Added migration commands

## Verification

After applying the fix:

1. **No tasks should have frequency `"ONCE"`**:
   ```sql
   SELECT COUNT(*) FROM activity_tasks WHERE frequency = 'ONCE';
   -- Should return 0
   ```

2. **One-time tasks should have frequency `"ONE_TIME"`**:
   ```sql
   SELECT COUNT(*) FROM activity_tasks WHERE frequency = 'ONE_TIME';
   ```

3. **Task completion should work correctly**:
   - One-time tasks can only be completed once
   - Subsequent completion attempts should be rejected

## Prevention

- All admin GraphQL resolvers now use proper mapping functions
- Tests ensure mapping consistency
- Future schema changes should include mapping function updates

## Commands Reference

```bash
# Check for tasks needing fix (safe, read-only)
make fix-task-frequency-dry-run

# Apply the fix to existing data
make fix-task-frequency

# Run mapping tests
go test ./internal/controller/admin/graphql/resolvers/ -v -run TestFrequencyMapping
```
