package resolvers

import (
	"testing"

	"github.com/stretchr/testify/assert"
	admin_gql_model "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

func TestFrequencyMapping(t *testing.T) {
	t.Run("Admin GraphQL ONE_TIME equals model ONE_TIME", func(t *testing.T) {
		adminFreq := admin_gql_model.TaskFrequencyOneTime
		modelFreq := model.FrequencyOneTime

		// Direct comparison should work since they're both "ONE_TIME"
		assert.Equal(t, string(modelFreq), string(adminFreq))
		assert.Equal(t, "ONE_TIME", string(adminFreq))
		assert.Equal(t, "ONE_TIME", string(modelFreq))
	})

	t.Run("Direct casting works for ONE_TIME", func(t *testing.T) {
		adminFreq := admin_gql_model.TaskFrequencyOneTime
		modelFreq := model.TaskFrequency(adminFreq) // Direct cast

		assert.Equal(t, model.FrequencyOneTime, modelFreq)
		assert.Equal(t, "ONE_TIME", string(modelFreq))
	})

	// Note: convertUserGQLFrequencyToAdminGQL is in a different file
	// so we skip this test for now

	t.Run("All admin frequencies map correctly", func(t *testing.T) {
		testCases := []struct {
			adminFreq admin_gql_model.TaskFrequency
			modelFreq model.TaskFrequency
		}{
			{admin_gql_model.TaskFrequencyOneTime, model.FrequencyOneTime},
			{admin_gql_model.TaskFrequencyDaily, model.FrequencyDaily},
			{admin_gql_model.TaskFrequencyUnlimited, model.FrequencyUnlimited},
		}

		for _, tc := range testCases {
			result := convertAdminGQLFrequencyToModel(tc.adminFreq)
			assert.Equal(t, tc.modelFreq, result,
				"Admin frequency %s should map to model frequency %s",
				tc.adminFreq, tc.modelFreq)
		}
	})

	t.Run("All model frequencies map correctly to admin", func(t *testing.T) {
		testCases := []struct {
			modelFreq model.TaskFrequency
			adminFreq admin_gql_model.TaskFrequency
		}{
			{model.FrequencyOneTime, admin_gql_model.TaskFrequencyOneTime},
			{model.FrequencyDaily, admin_gql_model.TaskFrequencyDaily},
			{model.FrequencyUnlimited, admin_gql_model.TaskFrequencyUnlimited},
		}

		for _, tc := range testCases {
			result := convertModelFrequencyToAdminGQL(tc.modelFreq)
			assert.Equal(t, tc.adminFreq, result,
				"Model frequency %s should map to admin frequency %s",
				tc.modelFreq, tc.adminFreq)
		}
	})
}
