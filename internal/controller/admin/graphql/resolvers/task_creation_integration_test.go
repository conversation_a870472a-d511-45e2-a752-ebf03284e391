package resolvers

import (
	"testing"

	"github.com/stretchr/testify/assert"
	admin_gql_model "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

func TestTaskCreationWithOneTimeFrequency(t *testing.T) {
	t.Run("Admin GraphQL ONE_TIME creates correct model frequency", func(t *testing.T) {
		// Simulate admin GraphQL input
		input := admin_gql_model.CreateTaskInput{
			CategoryID:  "2",
			Name:        "Test One-Time Task",
			Frequency:   admin_gql_model.TaskFrequencyOneTime,
			Points:      10,
			Description: stringPtr("Test description"),
		}

		// Simulate the task creation logic
		task := &model.ActivityTask{
			CategoryID: 2,
			Name:       input.Name,
			Frequency:  model.TaskFrequency(input.Frequency), // Direct cast
			Points:     input.Points,
		}

		// Verify the frequency is correctly set
		assert.Equal(t, model.FrequencyOneTime, task.Frequency)
		assert.Equal(t, "ONE_TIME", string(task.Frequency))
	})

	t.Run("All supported admin frequencies map correctly", func(t *testing.T) {
		testCases := []struct {
			name      string
			adminFreq admin_gql_model.TaskFrequency
			expected  model.TaskFrequency
		}{
			{"ONE_TIME", admin_gql_model.TaskFrequencyOneTime, model.FrequencyOneTime},
			{"DAILY", admin_gql_model.TaskFrequencyDaily, model.FrequencyDaily},
			{"UNLIMITED", admin_gql_model.TaskFrequencyUnlimited, model.FrequencyUnlimited},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				// Direct cast should work for all supported frequencies
				result := model.TaskFrequency(tc.adminFreq)
				assert.Equal(t, tc.expected, result, 
					"Admin frequency %s should cast to model frequency %s", 
					tc.adminFreq, tc.expected)
			})
		}
	})

	t.Run("Enum values are identical strings", func(t *testing.T) {
		// Verify that the enum values are actually the same strings
		assert.Equal(t, "ONE_TIME", string(admin_gql_model.TaskFrequencyOneTime))
		assert.Equal(t, "ONE_TIME", string(model.FrequencyOneTime))
		assert.Equal(t, "DAILY", string(admin_gql_model.TaskFrequencyDaily))
		assert.Equal(t, "DAILY", string(model.FrequencyDaily))
		assert.Equal(t, "UNLIMITED", string(admin_gql_model.TaskFrequencyUnlimited))
		assert.Equal(t, "UNLIMITED", string(model.FrequencyUnlimited))
	})
}

// Helper function to create string pointer
func stringPtr(s string) *string {
	return &s
}
